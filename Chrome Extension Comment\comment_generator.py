import time
import json
import re
import os
import random
import hashlib
from dotenv import load_dotenv
from Model_call.model_manager import get_model_manager
from post_processing_layer.post_proc import clean_and_complete_string
import logging

# Load environment variables
load_dotenv()

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize model manager
model_manager = get_model_manager()


def detect_language(text: str) -> str:
    """
    Detect the primary language of the text content with improved Hinglish detection.
    Only returns 'hinglish' when the input text actually contains Hindi/Hinglish words.

    Args:
        text (str): Text content to analyze

    Returns:
        str: Detected language ('english', 'hinglish', 'hindi', 'other')
    """
    try:
        # Convert to lowercase for analysis
        text_lower = text.lower()

        # Strictly Hindi/Hinglish words written in Roman script (removed common English words)
        hinglish_words = {
            # Core Hindi words in Roman script
            'aaj', 'kal', 'sab', 'kuch', 'koi', 'yeh', 'woh', 'hai', 'hain', 'tha', 'thi',
            'kar', 'karna', 'karte', 'kiya', 'kiye', 'hota', 'hoti', 'hote', 'ho', 'hun', 'hoon',
            'mein', 'main', 'se', 'ko', 'ka', 'ki', 'ke', 'par', 'pe', 'mera', 'meri', 'mere',
            'tera', 'teri', 'tere', 'uska', 'uski', 'uske', 'apna', 'apni', 'apne',
            'sochte', 'sochna', 'chahiye', 'chaahiye', 'zindagi', 'jindagi',
            'loge', 'lena', 'leke', 'dena', 'deke', 'milta', 'milti', 'milte',
            'badlega', 'badal', 'hisaab', 'hisab', 'tab', 'jab', 'kab', 'kyun', 'kyon',
            'kaise', 'kaisa', 'kaisi', 'kahan', 'yahan', 'wahan', 'abhi', 'phir', 'fir',
            'bilkul', 'sahi', 'galat', 'accha', 'achha', 'bura', 'bhi', 'baat', 'cheez', 'chiz',
            'log', 'logo', 'logon', 'sabko', 'sabse', 'sare', 'saare', 'pura', 'poora', 'adha',
            'thoda', 'bahut', 'bohot', 'zyada', 'jyada', 'sirf', 'bas', 'matlab', 'mtlb',
            'samjh', 'samajh', 'pata', 'malum', 'dekh', 'dekho', 'dekha', 'dekhe', 'sun', 'suno',
            'suna', 'sune', 'bol', 'bolo', 'bola', 'bole', 'keh', 'kaho', 'kaha', 'kahe',
            'paisa', 'paise', 'rupee', 'rupaye', 'ghar', 'kaam',
            'samay', 'din', 'raat', 'subah', 'shaam', 'dopahar',
            'dost', 'yaar', 'bhai', 'behen', 'mummy', 'papa',
            'desi', 'videsh',
            'padhai', 'shaadi',
            'khana', 'chai', 'paani', 'doodh',
            'auto', 'ola', 'metro',
            'dawai',
            'flipkart'
        }

        # Count Hindi/Devanagari characters (for pure Hindi)
        hindi_chars = len(re.findall(r'[\u0900-\u097F]', text))

        # Split text into words and analyze
        words = re.findall(r'\b[a-zA-Z]+\b', text_lower)
        total_words = len(words)

        if total_words == 0:
            return "english"  # Default fallback

        # Count Hinglish words (Hindi words in Roman script)
        hinglish_word_count = sum(1 for word in words if word in hinglish_words)

        # Comprehensive English-only words (including business/professional terms)
        english_only_words = {
            # Basic English words
            'the', 'and', 'or', 'but', 'if', 'then', 'when', 'where', 'why', 'how', 'what', 'who',
            'this', 'that', 'these', 'those', 'here', 'there', 'now', 'then', 'today', 'tomorrow',
            'yesterday', 'always', 'never', 'sometimes', 'often', 'usually', 'really', 'very',
            'quite', 'just', 'only', 'also', 'even', 'still', 'already', 'yet', 'again',
            'first', 'second', 'third', 'last', 'next', 'previous', 'before', 'after', 'during',
            'about', 'above', 'below', 'under', 'over', 'through', 'between', 'among', 'within',
            'without', 'against', 'towards', 'across', 'around', 'behind', 'beside', 'beyond',

            # Descriptive words
            'important', 'different', 'possible', 'available', 'necessary', 'interesting',
            'beautiful', 'wonderful', 'amazing', 'incredible', 'fantastic', 'excellent',
            'perfect', 'great', 'good', 'better', 'best', 'bad', 'worse', 'worst',
            'big', 'small', 'large', 'little', 'huge', 'tiny', 'long', 'short', 'high', 'low',
            'new', 'old', 'young', 'fresh', 'recent', 'modern', 'ancient', 'current', 'latest',
            'right', 'wrong', 'correct', 'incorrect', 'true', 'false', 'real', 'fake',
            'easy', 'difficult', 'hard', 'simple', 'complex', 'complicated', 'clear', 'unclear',

            # Business and professional English words
            'business', 'company', 'team', 'meeting', 'call', 'email', 'message', 'work', 'job',
            'success', 'plan', 'action', 'change', 'time', 'project', 'strategy', 'goal',
            'startup', 'technology', 'innovation', 'development', 'growth', 'leadership',
            'management', 'collaboration', 'productivity', 'efficiency', 'performance',
            'solution', 'platform', 'system', 'process', 'method', 'approach', 'framework',
            'analytics', 'data', 'insights', 'decisions', 'results', 'outcomes', 'impact',
            'opportunity', 'challenge', 'experience', 'potential', 'achievement', 'progress',
            'professional', 'career', 'industry', 'market', 'customer', 'client', 'service',
            'quality', 'value', 'benefit', 'advantage', 'feature', 'capability', 'skill',
            'knowledge', 'learning', 'training', 'education', 'expertise', 'talent',

            # Social media and digital terms
            'social', 'media', 'post', 'share', 'like', 'comment', 'follow', 'unfollow',
            'content', 'digital', 'online', 'internet', 'website', 'platform', 'network',

            # Common verbs and actions
            'create', 'build', 'develop', 'design', 'implement', 'execute', 'deliver',
            'achieve', 'accomplish', 'complete', 'finish', 'start', 'begin', 'launch',
            'improve', 'enhance', 'optimize', 'increase', 'decrease', 'reduce', 'manage',
            'organize', 'coordinate', 'communicate', 'collaborate', 'participate', 'contribute',
            'support', 'help', 'assist', 'guide', 'lead', 'inspire', 'motivate', 'encourage',
            'learn', 'understand', 'discover', 'explore', 'research', 'analyze', 'evaluate'
        }

        english_only_count = sum(1 for word in words if word in english_only_words)

        # Calculate ratios
        hinglish_ratio = hinglish_word_count / total_words
        english_only_ratio = english_only_count / total_words
        devanagari_ratio = hindi_chars / len(text) if len(text) > 0 else 0

        logger.info(f"Language detection - Total words: {total_words}, Hinglish words: {hinglish_word_count}, English-only words: {english_only_count}, Devanagari chars: {hindi_chars}")
        logger.info(f"Ratios - Hinglish: {hinglish_ratio:.2f}, English-only: {english_only_ratio:.2f}, Devanagari: {devanagari_ratio:.2f}")

        # Strict language detection logic - only return Hinglish when Hindi words are actually present
        if devanagari_ratio > 0.1:  # Significant Devanagari script
            if hinglish_ratio > 0.05 or english_only_ratio > 0.1:
                return "hinglish"  # Mixed script
            else:
                return "hindi"     # Pure Hindi
        elif hinglish_ratio >= 0.15:  # 15% or more Hinglish words - stricter threshold
            return "hinglish"
        elif hinglish_ratio >= 0.10 and english_only_ratio < 0.3:  # Some Hinglish, but not predominantly English
            return "hinglish"
        elif english_only_ratio >= 0.3:  # Predominantly English - lowered threshold for better English detection
            return "english"
        else:
            # Fallback: check for common Hinglish patterns (only strong indicators)
            hinglish_patterns = ['aaj kal', 'sab log', 'yeh hai', 'woh hai', 'kya hai', 'kaise hai',
                               'bilkul sahi', 'bahut accha', 'bohot achha', 'matlab hai', 'mein hai',
                               'se hai', 'ko hai', 'ka hai', 'ki hai', 'ke hai']

            for pattern in hinglish_patterns:
                if pattern in text_lower:
                    return "hinglish"

            # Final check: if we have any Hinglish words but low English-only ratio, it might be Hinglish
            if hinglish_ratio > 0.05 and english_only_ratio < 0.2:
                return "hinglish"

            return "english"  # Default fallback - prefer English when in doubt

    except Exception as e:
        logger.warning(f"Language detection failed: {str(e)}, defaulting to English")
        return "english"


def truncate_post_content(post_content: str, max_length: int = 2000) -> str:
    """
    Intelligently truncate long post content while preserving meaning.

    Args:
        post_content (str): Original post content
        max_length (int): Maximum length to keep

    Returns:
        str: Truncated post content with key points preserved
    """
    if len(post_content) <= max_length:
        return post_content

    # Try to truncate at sentence boundaries
    sentences = re.split(r'(?<=\.)\s+', post_content)
    truncated = ""

    for sentence in sentences:
        if len(truncated + sentence) <= max_length - 50:  # Leave room for "..."
            truncated += sentence + " "
        else:
            break

    # If we couldn't fit any complete sentences, just truncate at word boundary
    if not truncated.strip():
        words = post_content.split()
        truncated = " ".join(words[:max_length//6])  # Rough estimate of words

    # Add ellipsis if truncated
    if len(truncated.strip()) < len(post_content.strip()):
        truncated = truncated.strip() + "..."

    return truncated.strip()


def extract_key_points(post_content: str) -> str:
    """
    Extract key points from long posts for better comment generation.

    Args:
        post_content (str): Original post content

    Returns:
        str: Key points summary
    """
    # Split into paragraphs and sentences
    paragraphs = [p.strip() for p in post_content.split('\n\n') if p.strip()]

    key_points = []

    for paragraph in paragraphs[:5]:  # Limit to first 5 paragraphs
        # Look for key phrases that indicate main points
        if any(phrase in paragraph.lower() for phrase in [
            'here\'s the truth', 'the truth is', 'here\'s what', 'the reality is',
            'the biggest', 'most important', 'key point', 'bottom line',
            'the problem is', 'the solution', 'what i learned', 'lesson learned'
        ]):
            key_points.append(paragraph)
        elif len(paragraph) < 200:  # Short, punchy statements
            key_points.append(paragraph)

    # If no key points found, take first few sentences
    if not key_points:
        sentences = re.split(r'(?<=\.)\s+', post_content)
        key_points = sentences[:3]

    return " ".join(key_points[:3])  # Limit to 3 key points


def generate_content_hash(post_content: str, comment_type: str) -> str:
    """
    Generate a hash from post content and comment type for randomization seeding.

    Args:
        post_content (str): The post content
        comment_type (str): The comment type

    Returns:
        str: Hash string for seeding randomization
    """
    content_key = f"{post_content[:100]}{comment_type}"
    return hashlib.md5(content_key.encode()).hexdigest()


def select_diverse_openings(opening_strategies: list, post_content: str, comment_type: str, count: int = 8) -> list:
    """
    Select diverse opening strategies using advanced content-based randomization to maximize uniqueness.

    Args:
        opening_strategies (list): List of all available opening strategies
        post_content (str): The post content for seeding
        comment_type (str): The comment type
        count (int): Number of openings to select

    Returns:
        list: Selected diverse opening strategies
    """
    # Create multiple deterministic but varied seeds based on content
    content_hash = generate_content_hash(post_content, comment_type)

    # Use content length and word count for additional entropy
    content_length_seed = len(post_content) % 1000
    word_count_seed = len(post_content.split()) % 100
    first_word_seed = hash(post_content.split()[0] if post_content.split() else "default") % 1000
    last_word_seed = hash(post_content.split()[-1] if post_content.split() else "default") % 1000

    # Create multiple hash-based seeds for maximum diversity with additional entropy
    seed1 = int(content_hash[:8], 16) + content_length_seed + first_word_seed
    seed2 = int(content_hash[8:16], 16) + word_count_seed + last_word_seed
    seed3 = int(content_hash[16:24], 16) + len(comment_type) + hash(post_content[:30]) % 500
    seed4 = int(content_hash[24:32], 16) + hash(post_content[-30:]) % 1000 + len(post_content.split())

    # Additional seed based on character distribution for maximum uniqueness
    char_distribution_seed = sum(ord(c) for c in post_content[:20]) % 10000
    seed5 = seed1 + seed2 + char_distribution_seed

    # Create multiple randomized selections with different algorithms
    all_selections = []

    # Selection 1: Content-hash based deterministic selection (primary uniqueness driver)
    random.seed(seed1)
    primary_index = seed1 % len(opening_strategies)
    primary_selection = opening_strategies[primary_index]
    all_selections.append(primary_selection)

    # Selection 2: Secondary selections using different seeds
    random.seed(seed2)
    remaining_after_primary = [s for s in opening_strategies if s != primary_selection]
    if remaining_after_primary:
        selection2 = random.sample(remaining_after_primary, min(count//2, len(remaining_after_primary)))
        all_selections.extend(selection2)

    # Selection 3: Weighted towards middle of list
    random.seed(seed3)
    mid_start = len(opening_strategies) // 4
    mid_end = 3 * len(opening_strategies) // 4
    mid_strategies = [s for s in opening_strategies[mid_start:mid_end] if s not in all_selections]
    if mid_strategies:
        selection3 = random.sample(mid_strategies, min(count//3, len(mid_strategies)))
        all_selections.extend(selection3)

    # Selection 4: Weighted towards end of list
    random.seed(seed4)
    end_strategies = [s for s in opening_strategies[len(opening_strategies)//2:] if s not in all_selections]
    if end_strategies:
        selection4 = random.sample(end_strategies, min(count//4, len(end_strategies)))
        all_selections.extend(selection4)

    # Selection 5: Content-aware selection based on keywords
    random.seed(seed5)
    content_lower = post_content.lower()

    # Prefer certain openings based on content characteristics
    if any(word in content_lower for word in ['experience', 'journey', 'story', 'personal']):
        experience_openings = [o for o in opening_strategies if o not in all_selections and any(word in o.lower() for word in ['experience', 'journey', 'reminds', 'memory'])]
        if experience_openings:
            selection5 = random.sample(experience_openings, min(2, len(experience_openings)))
            all_selections.extend(selection5)

    if any(word in content_lower for word in ['insight', 'data', 'research', 'analysis']):
        insight_openings = [o for o in opening_strategies if o not in all_selections and any(word in o.lower() for word in ['observe', 'data', 'research', 'experience'])]
        if insight_openings:
            selection6 = random.sample(insight_openings, min(2, len(insight_openings)))
            all_selections.extend(selection6)

    # Combine and deduplicate while maintaining order diversity
    combined = list(dict.fromkeys(all_selections))

    # If we need more, add additional random selections from unused openings
    if len(combined) < count:
        remaining = [s for s in opening_strategies if s not in combined]
        if remaining:
            random.seed(seed1 + seed2 + seed3 + seed5)
            additional = random.sample(remaining, min(count - len(combined), len(remaining)))
            combined.extend(additional)

    # Final shuffle with content-based seed to ensure the order is also diverse
    # But keep the first element stable for maximum first-opening uniqueness
    first_element = combined[0] if combined else None
    if len(combined) > 1:
        rest = combined[1:]
        random.seed(seed5)
        random.shuffle(rest)
        combined = [first_element] + rest

    return combined[:count]


def create_context_aware_instructions(post_content: str, comment_type: str) -> str:
    """
    Create context-aware instructions based on post content analysis.

    Args:
        post_content (str): The post content to analyze
        comment_type (str): The comment type

    Returns:
        str: Context-specific instructions
    """
    content_lower = post_content.lower()

    # Analyze post characteristics
    is_personal_story = any(word in content_lower for word in ['my', 'i', 'me', 'personal', 'journey', 'experience'])
    is_business_focused = any(word in content_lower for word in ['business', 'company', 'startup', 'team', 'leadership'])
    is_technical = any(word in content_lower for word in ['technology', 'ai', 'data', 'software', 'digital'])
    is_motivational = any(word in content_lower for word in ['success', 'growth', 'achieve', 'goal', 'dream'])
    is_question_based = '?' in post_content

    instructions = []

    if is_personal_story:
        instructions.append("The post shares personal experiences - respond with empathy and relatability")
    if is_business_focused:
        instructions.append("Business context detected - use professional yet conversational tone")
    if is_technical:
        instructions.append("Technical content - balance expertise with accessibility")
    if is_motivational:
        instructions.append("Motivational content - match the inspiring energy appropriately")
    if is_question_based:
        instructions.append("Post contains questions - engage thoughtfully with the inquiry")

    if not instructions:
        instructions.append("Respond authentically to the specific content and tone of the post")

    return " | ".join(instructions)


def create_comment_prompt(post_content: str, comment_type: str, detected_language: str) -> str:
    """
    Create a specialized prompt for LinkedIn comment generation.

    Args:
        post_content (str): The original LinkedIn post content
        comment_type (str): Type of comment to generate
        detected_language (str): Detected language of the post

    Returns:
        str: Formatted prompt for comment generation
    """

    # Handle long posts by extracting key points and truncating
    original_length = len(post_content)
    if original_length > 1500:
        logger.info(f"Long post detected ({original_length} chars), extracting key points")
        key_points = extract_key_points(post_content)
        truncated_content = truncate_post_content(post_content, 1500)

        # Use both key points and truncated content for context
        processed_content = f"Key Points: {key_points}\n\nFull Context: {truncated_content}"
    else:
        processed_content = post_content

    # Comment type specifications with diverse opening strategies
    comment_types = {
        "positive": {
            "description": "Supportive and encouraging response",
            "tone_guide": "Express genuine appreciation, agreement, or encouragement. Be warm and supportive.",
            "opening_strategies": {
                "english": [
                    # Personal resonance starters
                    "This really resonates with me...", "Your words hit differently today...", "Something about this just clicks...",
                    "This speaks to me on so many levels...", "Reading this felt like a lightbulb moment...", "This captures exactly what I've been feeling...",
                    "Your perspective here really lands...", "This struck a chord with me...", "There's something profound about this...",
                    "This feels so timely and relevant...", "Your insight here is everything...", "This just made my day brighter...",

                    # Appreciation and agreement
                    "Love how you've framed this...", "The way you've articulated this is brilliant...", "You've captured something important here...",
                    "This is exactly the conversation we need...", "Your approach to this is refreshing...", "The clarity in your thinking shines through...",
                    "You've hit on something crucial...", "This perspective is so needed right now...", "The depth of your insight here...",
                    "Your thoughtfulness comes through beautifully...", "This is such a valuable reminder...", "The wisdom in this post...",

                    # Memory and experience triggers
                    "This takes me back to a conversation I had...", "Reading this reminded me why I love...", "This brings back memories of when...",
                    "Your words echo something my mentor once said...", "This reminds me of a lesson I learned...", "I'm thinking back to when I first...",
                    "This connects to something I experienced...", "Your point here reminds me of...", "This brings to mind a time when...",
                    "Reading this, I'm reminded of...", "This takes me back to my early days...", "Your words bring back a memory...",

                    # Emotional responses
                    "This gave me chills in the best way...", "Reading this just made me smile...", "This warmed my heart...",
                    "I felt this in my bones...", "This just lifted my spirits...", "Your words are like a breath of fresh air...",
                    "This hit me right in the feels...", "Reading this was like a warm hug...", "This just brightened my entire morning...",
                    "Your energy in this post is contagious...", "This filled me with hope...", "Reading this was exactly what I needed...",

                    # Observational starters
                    "What strikes me most about this...", "The thing that stands out here...", "What I find most compelling...",
                    "The beauty of your message lies in...", "What resonates most deeply...", "The power of this perspective...",
                    "What makes this so impactful...", "The strength of your argument...", "What I appreciate most here...",
                    "The elegance of your thinking...", "What draws me to this idea...", "The authenticity in your voice...",

                    # Future-focused positivity
                    "This gives me so much hope for...", "Your vision here inspires me to...", "This makes me excited about...",
                    "Reading this motivates me to...", "This sparks ideas about how we can...", "Your perspective opens up possibilities for...",
                    "This challenges me to think about...", "Your insight pushes me to consider...", "This inspires a new way of thinking about...",
                    "Your words encourage me to...", "This makes me want to explore...", "Reading this energizes me to..."
                ],
                "hinglish": [
                    # Personal resonance with Hindi touch
                    "This really resonates yaar...", "Your words hit differently today...", "Something about this just clicks, bilkul...",
                    "This speaks to me, matlab really deeply...", "Reading this felt like a lightbulb moment...", "This captures exactly what I've been feeling...",
                    "Your perspective here really lands, sahi mein...", "This struck a chord with me...", "There's something profound about this...",
                    "This feels so timely, bilkul perfect timing...", "Your insight here is everything...", "This just made my day brighter...",

                    # Appreciation with Hinglish flair
                    "Love how you've framed this yaar...", "The way you've articulated this, bahut brilliant...", "You've captured something important here...",
                    "This is exactly the conversation we need...", "Your approach to this is refreshing...", "The clarity in your thinking, matlab wow...",
                    "You've hit on something crucial...", "This perspective is so needed, bilkul sahi time...", "The depth of your insight here...",
                    "Your thoughtfulness comes through beautifully...", "This is such a valuable reminder...", "The wisdom in this post, yaar...",

                    # Memory triggers with Hindi
                    "This takes me back to a conversation, matlab...", "Reading this reminded me why I love...", "This brings back memories of when...",
                    "Your words echo something, bilkul same feeling...", "This reminds me of a lesson I learned...", "I'm thinking back to when I first...",
                    "This connects to something I experienced...", "Your point here reminds me of...", "This brings to mind a time when...",
                    "Reading this, I'm reminded of...", "This takes me back to my early days...", "Your words bring back a memory, yaar...",

                    # Emotional responses with Hinglish
                    "This gave me chills, bahut accha feeling...", "Reading this just made me smile...", "This warmed my heart...",
                    "I felt this in my bones, bilkul...", "This just lifted my spirits...", "Your words are like a breath of fresh air...",
                    "This hit me right in the feels...", "Reading this was like a warm hug...", "This just brightened my entire morning...",
                    "Your energy in this post, matlab contagious hai...", "This filled me with hope...", "Reading this was exactly what I needed...",

                    # Observational with Hindi touch
                    "What strikes me most about this...", "The thing that stands out here...", "What I find most compelling, matlab...",
                    "The beauty of your message lies in...", "What resonates most deeply...", "The power of this perspective...",
                    "What makes this so impactful...", "The strength of your argument...", "What I appreciate most here...",
                    "The elegance of your thinking, yaar...", "What draws me to this idea...", "The authenticity in your voice...",

                    # Future-focused with Hinglish
                    "This gives me so much hope for...", "Your vision here inspires me to...", "This makes me excited about...",
                    "Reading this motivates me to...", "This sparks ideas, matlab how we can...", "Your perspective opens up possibilities for...",
                    "This challenges me to think about...", "Your insight pushes me to consider...", "This inspires a new way of thinking...",
                    "Your words encourage me to...", "This makes me want to explore...", "Reading this energizes me, bilkul..."
                ]
            }
        },
        "constructive": {
            "description": "Thoughtful feedback or additional perspective",
            "tone_guide": "Provide respectful, thoughtful feedback or add a different perspective. Be constructive and professional.",
            "opening_strategies": {
                "english": [
                    # Building and expanding
                    "Building on this thought...", "Expanding on your idea...", "Taking this a step further...",
                    "Adding another layer to consider...", "Building from your foundation here...", "Extending your logic...",
                    "Amplifying this perspective...", "Weaving in another thread...", "Connecting this to another angle...",
                    "Layering in some additional context...", "Bridging this with another consideration...", "Complementing your view with...",

                    # Alternative perspectives
                    "From a different vantage point...", "Looking at this through another lens...", "Considering the flip side...",
                    "From where I sit, I'd also add...", "Another way to frame this might be...", "Shifting the angle slightly...",
                    "Through a different filter...", "From another corner of the room...", "Viewing this from the other side...",
                    "Approaching this from a different direction...", "Considering an alternative pathway...", "Looking at this from the ground up...",

                    # Experience-based additions
                    "In my experience, I'd also consider...", "From what I've observed, perhaps...", "Having walked this path, I'd add...",
                    "My journey has taught me that...", "From the trenches, I'd suggest...", "Having been there, I'd also think about...",
                    "Through trial and error, I've learned...", "From a practitioner's perspective...", "Having navigated this before...",
                    "My experience suggests we might also...", "From the field, I'd add...", "Having lived this, I'd consider...",

                    # Collaborative enhancement
                    "What if we also explored...", "Perhaps we could strengthen this by...", "I wonder if we might also...",
                    "Could we enhance this by considering...", "What about also factoring in...", "Might we also benefit from...",
                    "I'm thinking we could also...", "Perhaps there's room to also...", "What if we layered in...",
                    "Could we also weave in...", "I'm wondering about also including...", "What about also addressing...",

                    # Nuanced additions
                    "There's a nuance here that might be worth...", "One subtlety I'd add...", "A refinement to consider...",
                    "There's an edge case that comes to mind...", "A wrinkle worth considering...", "One dimension that might enhance this...",
                    "A subtle but important addition...", "There's a layer here that could...", "One aspect that might strengthen this...",
                    "A consideration that could deepen this...", "There's a thread here worth pulling...", "One element that might round this out...",

                    # Respectful challenge
                    "I'm curious about one aspect...", "One area where I'd love to dig deeper...", "There's one piece I'm wrestling with...",
                    "I find myself wondering about...", "One element that gives me pause...", "There's a tension here I'm exploring...",
                    "I'm grappling with one component...", "One dimension that intrigues me...", "There's a complexity here that...",
                    "I'm sitting with one question...", "One facet that deserves more exploration...", "There's a dynamic here that..."
                ],
                "hinglish": [
                    # Building with Hindi touch
                    "Building on this thought, matlab...", "Expanding on your idea...", "Taking this a step further...",
                    "Adding another layer to consider...", "Building from your foundation here...", "Extending your logic, bilkul...",
                    "Amplifying this perspective...", "Weaving in another thread...", "Connecting this to another angle...",
                    "Layering in some additional context...", "Bridging this with another consideration...", "Complementing your view with...",

                    # Alternative perspectives with Hinglish
                    "From a different vantage point...", "Looking at this through another lens...", "Considering the flip side, yaar...",
                    "From where I sit, I'd also add...", "Another way to frame this might be...", "Shifting the angle slightly...",
                    "Through a different filter...", "From another corner of the room...", "Viewing this from the other side...",
                    "Approaching this differently, matlab...", "Considering an alternative pathway...", "Looking at this from the ground up...",

                    # Experience-based with Hindi
                    "In my experience, I'd also consider...", "From what I've observed, perhaps...", "Having walked this path, I'd add...",
                    "My journey has taught me that...", "From the trenches, I'd suggest...", "Having been there, bilkul I'd think...",
                    "Through trial and error, I've learned...", "From a practitioner's perspective...", "Having navigated this before...",
                    "My experience suggests, matlab we might...", "From the field, I'd add...", "Having lived this, I'd consider...",

                    # Collaborative with Hinglish
                    "What if we also explored...", "Perhaps we could strengthen this by...", "I wonder if we might also...",
                    "Could we enhance this by considering...", "What about also factoring in...", "Might we also benefit from...",
                    "I'm thinking we could also...", "Perhaps there's room to also...", "What if we layered in, yaar...",
                    "Could we also weave in...", "I'm wondering about also including...", "What about also addressing...",

                    # Nuanced with Hindi touch
                    "There's a nuance here, matlab worth...", "One subtlety I'd add...", "A refinement to consider...",
                    "There's an edge case that comes to mind...", "A wrinkle worth considering...", "One dimension that might enhance this...",
                    "A subtle but important addition...", "There's a layer here that could...", "One aspect that might strengthen this...",
                    "A consideration that could deepen this...", "There's a thread here worth pulling...", "One element that might round this out...",

                    # Respectful challenge with Hinglish
                    "I'm curious about one aspect...", "One area where I'd love to dig deeper...", "There's one piece I'm wrestling with...",
                    "I find myself wondering about...", "One element that gives me pause...", "There's a tension here I'm exploring...",
                    "I'm grappling with one component...", "One dimension that intrigues me...", "There's a complexity here that...",
                    "I'm sitting with one question, yaar...", "One facet that deserves more exploration...", "There's a dynamic here that..."
                ]
            }
        },
        "question": {
            "description": "Curious inquiry to engage further",
            "tone_guide": "Ask genuine, thoughtful questions that show interest and encourage discussion.",
            "opening_strategies": {
                "english": [
                    # Direct curiosity
                    "I'm curious about...", "I'm wondering about...", "I find myself asking...",
                    "What intrigues me is...", "I'm drawn to understand...", "I'm fascinated by...",
                    "Something I'm pondering...", "I'm really interested in...", "What captures my attention...",
                    "I'm eager to learn more about...", "I'm genuinely curious about...", "What strikes me as interesting...",

                    # Thought-provoking starters
                    "This got me thinking...", "Your post sparked a question...", "Reading this made me wonder...",
                    "This triggered a thought...", "Your words got me reflecting on...", "This opened up a question for me...",
                    "Something about this made me consider...", "This brought up an interesting point...", "Your perspective raised a question...",
                    "This made me pause and think about...", "Reading this, I started wondering...", "This sparked something in my mind...",

                    # Exploratory questions
                    "I'd love to explore...", "I'm keen to understand...", "I'd be interested to know...",
                    "I'm hoping you might share...", "I'd appreciate your thoughts on...", "I'm curious to hear your take on...",
                    "I'd love to dig deeper into...", "I'm interested in your perspective on...", "I'd value your insight on...",
                    "I'm drawn to learn more about...", "I'd love to understand better...", "I'm hoping to gain clarity on...",

                    # Observational questions
                    "What strikes me about this...", "One thing that makes me wonder...", "Something that caught my attention...",
                    "What I find particularly interesting...", "One aspect that raises questions...", "What draws my curiosity...",
                    "Something that stands out to me...", "What I'm most curious about...", "One element that intrigues me...",
                    "What I find myself questioning...", "Something that piques my interest...", "What I'm most drawn to explore...",

                    # Collaborative inquiry
                    "Have you found that...", "In your experience, do you think...", "I'm wondering if you've noticed...",
                    "Do you see a connection between...", "Have you encountered situations where...", "I'm curious if you've observed...",
                    "Do you think there's a relationship between...", "Have you seen patterns where...", "I'm wondering about your experience with...",
                    "Do you find that...", "Have you noticed whether...", "I'm curious about your thoughts on whether...",

                    # Deeper exploration
                    "What's behind this idea that...", "I'm trying to understand how...", "What drives the thinking that...",
                    "I'm grappling with the concept of...", "What's the mechanism behind...", "I'm trying to piece together how...",
                    "What's the root of...", "I'm working to understand why...", "What's the foundation for...",
                    "I'm exploring the idea that...", "What's the logic behind...", "I'm investigating whether..."
                ],
                "hinglish": [
                    # Direct curiosity with Hindi
                    "I'm curious about this, yaar...", "I'm wondering about...", "I find myself asking...",
                    "What intrigues me is...", "I'm drawn to understand...", "I'm fascinated by this, bilkul...",
                    "Something I'm pondering...", "I'm really interested in...", "What captures my attention...",
                    "I'm eager to learn more about...", "I'm genuinely curious about...", "What strikes me as interesting...",

                    # Thought-provoking with Hinglish
                    "This got me thinking, matlab...", "Your post sparked a question...", "Reading this made me wonder...",
                    "This triggered a thought...", "Your words got me reflecting on...", "This opened up a question for me...",
                    "Something about this made me consider...", "This brought up an interesting point...", "Your perspective raised a question...",
                    "This made me pause and think, yaar...", "Reading this, I started wondering...", "This sparked something in my mind...",

                    # Exploratory with Hindi touch
                    "I'd love to explore...", "I'm keen to understand...", "I'd be interested to know...",
                    "I'm hoping you might share...", "I'd appreciate your thoughts on...", "I'm curious to hear your take, matlab...",
                    "I'd love to dig deeper into...", "I'm interested in your perspective on...", "I'd value your insight on...",
                    "I'm drawn to learn more about...", "I'd love to understand better...", "I'm hoping to gain clarity on...",

                    # Observational with Hinglish
                    "What strikes me about this...", "One thing that makes me wonder...", "Something that caught my attention...",
                    "What I find particularly interesting...", "One aspect that raises questions...", "What draws my curiosity, yaar...",
                    "Something that stands out to me...", "What I'm most curious about...", "One element that intrigues me...",
                    "What I find myself questioning...", "Something that piques my interest...", "What I'm most drawn to explore...",

                    # Collaborative with Hindi
                    "Have you found that...", "In your experience, do you think...", "I'm wondering if you've noticed...",
                    "Do you see a connection between...", "Have you encountered situations where...", "I'm curious if you've observed...",
                    "Do you think there's a relationship, matlab...", "Have you seen patterns where...", "I'm wondering about your experience with...",
                    "Do you find that...", "Have you noticed whether...", "I'm curious about your thoughts, yaar...",

                    # Deeper exploration with Hinglish
                    "What's behind this idea that...", "I'm trying to understand how...", "What drives the thinking that...",
                    "I'm grappling with the concept of...", "What's the mechanism behind...", "I'm trying to piece together how...",
                    "What's the root of...", "I'm working to understand why...", "What's the foundation for...",
                    "I'm exploring the idea that...", "What's the logic behind...", "I'm investigating whether, bilkul..."
                ]
            }
        },
        "personal_experience": {
            "description": "Relating personal experience to the topic",
            "tone_guide": "Share a brief, relevant personal experience that connects to the post. Be authentic and relatable.",
            "opening_strategies": {
                "english": [
                    # Memory triggers
                    "This takes me back to when...", "This reminds me of a time when...", "Reading this brought back memories of...",
                    "This transported me to a moment when...", "Your words echo an experience I had...", "This brings to mind when I...",
                    "I'm flashing back to when...", "This instantly reminded me of...", "Your post triggered a memory of...",
                    "This takes me straight back to...", "I'm thinking of a time when...", "This brings up a memory from when...",

                    # Direct relation
                    "I can totally relate to this...", "I've been in this exact situation...", "This hits so close to home...",
                    "I know this feeling all too well...", "I've walked this path before...", "This resonates with my own experience...",
                    "I've lived through something similar...", "This mirrors my own journey...", "I recognize this struggle...",
                    "I've faced this same challenge...", "This feels familiar from my own life...", "I've navigated similar waters...",

                    # Emotional connection
                    "Your words hit close to home...", "This struck a personal chord...", "I felt this in my bones...",
                    "This resonates deeply with my experience...", "Your story echoes my own...", "This touched something personal...",
                    "I see myself in your words...", "This connects to my core...", "Your experience mirrors mine...",
                    "This speaks to my soul...", "I feel this on a personal level...", "This hits different because...",

                    # Journey parallels
                    "My journey has been similar...", "I've traveled a comparable path...", "Our experiences seem to align...",
                    "I've been on a similar trajectory...", "My story has similar chapters...", "I've faced parallel challenges...",
                    "Our paths seem to have crossed...", "I've encountered similar crossroads...", "My experience echoes yours...",
                    "I've been through comparable phases...", "Our journeys share common threads...", "I've walked similar ground...",

                    # Learning and growth
                    "I learned this lesson the hard way...", "My experience taught me that...", "I discovered this truth when...",
                    "I came to realize this when...", "My journey showed me that...", "I learned firsthand that...",
                    "Experience taught me that...", "I found out the hard way that...", "My path revealed that...",
                    "I discovered through experience that...", "Life taught me that...", "I learned through trial that...",

                    # Vulnerability and honesty
                    "I've been there, and honestly...", "If I'm being completely honest...", "I'll admit, I've struggled with...",
                    "I've been in that dark place...", "I know the weight of...", "I've felt that same pressure...",
                    "I've carried that burden too...", "I understand that feeling because...", "I've wrestled with similar demons...",
                    "I've been in those shoes...", "I know what it's like to...", "I've felt that same uncertainty..."
                ],
                "hinglish": [
                    # Memory triggers with Hindi
                    "This takes me back to when...", "This reminds me of a time when...", "Reading this brought back memories of...",
                    "This transported me to a moment when...", "Your words echo an experience I had...", "This brings to mind when I...",
                    "I'm flashing back to when...", "This instantly reminded me of...", "Your post triggered a memory, yaar...",
                    "This takes me straight back to...", "I'm thinking of a time when...", "This brings up a memory from when...",

                    # Direct relation with Hinglish
                    "I can totally relate to this, bilkul...", "I've been in this exact situation...", "This hits so close to home...",
                    "I know this feeling all too well...", "I've walked this path before...", "This resonates with my own experience...",
                    "I've lived through something similar...", "This mirrors my own journey...", "I recognize this struggle, yaar...",
                    "I've faced this same challenge...", "This feels familiar from my own life...", "I've navigated similar waters...",

                    # Emotional connection with Hindi
                    "Your words hit close to home...", "This struck a personal chord...", "I felt this in my bones, bilkul...",
                    "This resonates deeply with my experience...", "Your story echoes my own...", "This touched something personal...",
                    "I see myself in your words...", "This connects to my core...", "Your experience mirrors mine...",
                    "This speaks to my soul, matlab...", "I feel this on a personal level...", "This hits different because...",

                    # Journey parallels with Hinglish
                    "My journey has been similar...", "I've traveled a comparable path...", "Our experiences seem to align...",
                    "I've been on a similar trajectory...", "My story has similar chapters...", "I've faced parallel challenges...",
                    "Our paths seem to have crossed...", "I've encountered similar crossroads...", "My experience echoes yours, yaar...",
                    "I've been through comparable phases...", "Our journeys share common threads...", "I've walked similar ground...",

                    # Learning with Hindi touch
                    "I learned this lesson the hard way...", "My experience taught me that...", "I discovered this truth when...",
                    "I came to realize this when...", "My journey showed me that...", "I learned firsthand that...",
                    "Experience taught me that...", "I found out the hard way, matlab...", "My path revealed that...",
                    "I discovered through experience that...", "Life taught me that...", "I learned through trial that...",

                    # Vulnerability with Hinglish
                    "I've been there, and honestly...", "If I'm being completely honest...", "I'll admit, I've struggled with...",
                    "I've been in that dark place...", "I know the weight of...", "I've felt that same pressure...",
                    "I've carried that burden too...", "I understand that feeling because...", "I've wrestled with similar demons...",
                    "I've been in those shoes, yaar...", "I know what it's like to...", "I've felt that same uncertainty..."
                ]
            }
        },
        "insight": {
            "description": "Adding valuable expertise or knowledge",
            "tone_guide": "Share professional insights, expertise, or valuable information that adds to the discussion.",
            "opening_strategies": {
                "english": [
                    # Experience-based insights
                    "From my experience in this space...", "What I've observed over the years...", "In my field, we've found that...",
                    "My work has taught me that...", "I've seen this pattern before...", "Professional experience tells me...",
                    "Having worked in this area...", "Through years of practice...", "My background has shown me...",
                    "From the trenches, I can say...", "Working closely with this...", "My expertise suggests...",

                    # Data and research
                    "Research consistently shows...", "Industry data suggests...", "The data backs this up...",
                    "Studies indicate that...", "Evidence points to...", "Research reveals that...",
                    "The numbers tell us...", "Data consistently demonstrates...", "Analysis shows that...",
                    "Empirical evidence suggests...", "The research landscape indicates...", "Statistical trends reveal...",

                    # Pattern recognition
                    "I've noticed a pattern where...", "There's a trend I've observed...", "A common thread I see...",
                    "What consistently emerges is...", "The pattern that keeps appearing...", "I've tracked this phenomenon...",
                    "A recurring theme I've identified...", "The consistent factor I see...", "What repeatedly surfaces...",
                    "The underlying pattern here...", "I've documented this trend...", "The consistent variable is...",

                    # Industry perspective
                    "From an industry standpoint...", "The sector perspective on this...", "Industry best practices suggest...",
                    "Market dynamics show that...", "The competitive landscape reveals...", "Industry leaders recognize...",
                    "Sector analysis indicates...", "The market has taught us...", "Industry evolution shows...",
                    "Professional standards suggest...", "The field has learned that...", "Industry wisdom tells us...",

                    # Analytical insights
                    "The underlying mechanism here...", "What's really driving this is...", "The root cause analysis shows...",
                    "Breaking this down reveals...", "The fundamental principle at play...", "Digging deeper, we find...",
                    "The core dynamic here is...", "What's beneath the surface...", "The systemic factor is...",
                    "The structural element driving this...", "The foundational aspect here...", "The key variable appears to be...",

                    # Forward-looking insights
                    "This trend suggests that...", "The trajectory indicates...", "Looking ahead, this implies...",
                    "The implications for the future...", "This signals a shift toward...", "The emerging pattern suggests...",
                    "This points to a future where...", "The direction this is heading...", "What this means going forward...",
                    "The long-term implications...", "This foreshadows...", "The evolution suggests..."
                ],
                "hinglish": [
                    # Experience-based with Hindi
                    "From my experience in this space...", "What I've observed over the years...", "In my field, we've found that...",
                    "My work has taught me that...", "I've seen this pattern before...", "Professional experience tells me...",
                    "Having worked in this area...", "Through years of practice...", "My background has shown me...",
                    "From the trenches, I can say...", "Working closely with this...", "My expertise suggests, matlab...",

                    # Data and research with Hinglish
                    "Research consistently shows...", "Industry data suggests, matlab...", "The data backs this up...",
                    "Studies indicate that...", "Evidence points to...", "Research reveals that...",
                    "The numbers tell us...", "Data consistently demonstrates...", "Analysis shows that...",
                    "Empirical evidence suggests...", "The research landscape indicates...", "Statistical trends reveal...",

                    # Pattern recognition with Hindi
                    "I've noticed a pattern where...", "There's a trend I've observed...", "A common thread I see...",
                    "What consistently emerges is...", "The pattern that keeps appearing...", "I've tracked this phenomenon...",
                    "A recurring theme I've identified...", "The consistent factor I see...", "What repeatedly surfaces...",
                    "The underlying pattern here...", "I've documented this trend...", "The consistent variable is, bilkul...",

                    # Industry perspective with Hinglish
                    "From an industry standpoint...", "The sector perspective on this...", "Industry best practices suggest...",
                    "Market dynamics show that...", "The competitive landscape reveals...", "Industry leaders recognize...",
                    "Sector analysis indicates...", "The market has taught us...", "Industry evolution shows...",
                    "Professional standards suggest...", "The field has learned that...", "Industry wisdom tells us, yaar...",

                    # Analytical insights with Hindi
                    "The underlying mechanism here...", "What's really driving this is...", "The root cause analysis shows...",
                    "Breaking this down reveals...", "The fundamental principle at play...", "Digging deeper, we find...",
                    "The core dynamic here is...", "What's beneath the surface...", "The systemic factor is...",
                    "The structural element driving this...", "The foundational aspect here...", "The key variable appears to be...",

                    # Forward-looking with Hinglish
                    "This trend suggests that...", "The trajectory indicates...", "Looking ahead, this implies...",
                    "The implications for the future...", "This signals a shift toward...", "The emerging pattern suggests...",
                    "This points to a future where...", "The direction this is heading...", "What this means going forward...",
                    "The long-term implications...", "This foreshadows...", "The evolution suggests, matlab..."
                ]
            }
        }
    }
    
    # Get comment type data
    comment_data = comment_types.get(comment_type.lower(), comment_types["positive"])

    # Language-specific instructions
    language_instructions = {
        "english": "Write in clear, natural English.",
        "hinglish": """Write in moderate Hinglish style - primarily English with selective Hindi words for authenticity.
        IMPORTANT: Use English sentence structure and grammar as the foundation, then add Hindi words strategically (20-30% of words).
        Focus on: Common expressions ('bilkul', 'sahi', 'yaar'), emphasis words ('bahut', 'really'), connectors ('matlab', 'basically').
        Examples: 'Absolutely right, bilkul sahi!', 'This is really inspiring yaar!', 'Great point, bahut achha!', 'Thanks for sharing, matlab this is exactly what we need!'
        AVOID: Heavy Hindi phrases or complex Hindi sentence structures. Keep it professional and English-dominant.""",
        "hindi": "Write in Hindi using Devanagari script.",
        "other": "Match the language style and tone of the original post."
    }

    language_instruction = language_instructions.get(detected_language, language_instructions["english"])
    all_opening_strategies = comment_data["opening_strategies"].get(detected_language, comment_data["opening_strategies"]["english"])

    # Select diverse openings using intelligent randomization
    selected_openings = select_diverse_openings(all_opening_strategies, post_content, comment_type, count=8)

    # Generate context-aware instructions
    context_instructions = create_context_aware_instructions(post_content, comment_type)
    
    prompt = f"""
You are an expert at writing authentic, human-like LinkedIn comments that engage meaningfully with posts. Your goal is to create a natural, conversational comment that sounds like it was written by a real professional.

**POST CONTENT TO COMMENT ON:**
{processed_content}

**COMMENT TYPE:** {comment_type.upper()}
**COMMENT PURPOSE:** {comment_data['description']}

**TONE GUIDANCE:** {comment_data['tone_guide']}

**LANGUAGE INSTRUCTION:** {language_instruction}

**CONTEXT-SPECIFIC GUIDANCE:** {context_instructions}

**DIVERSE OPENING STRATEGIES (select the most contextually appropriate one):**
{' | '.join(selected_openings)}

**CRITICAL REQUIREMENTS FOR MAXIMUM DIVERSITY:**
1. **UNIQUE OPENINGS:** Each comment must start differently - avoid any repetitive patterns or formulaic beginnings
2. **CONTEXT-SPECIFIC:** Choose an opening that directly relates to the specific content, not a generic response
3. **NATURAL VARIATION:** Use the opening as inspiration but adapt it to fit the exact context and tone
4. **AUTHENTIC VOICE:** Write like a real person responding spontaneously, not following a template
5. **CONTENT-DRIVEN:** Let the specific post content guide your opening choice and overall response

**WRITING REQUIREMENTS:**
1. **Maximum Opening Diversity:** Ensure your opening is unique and contextually relevant - never use generic starters
2. **Content Awareness:** Reference specific elements from the post to show genuine engagement
3. **Length:** Keep it concise (40-50 words maximum) - LinkedIn comments should be brief and impactful
4. **Authenticity:** Write like a real person having a genuine conversation - use natural language patterns
5. **Engagement:** Make it conversational and encourage further discussion when appropriate
6. **Relevance:** Directly relate to the specific content and key points of the post
7. **Professional:** Maintain a professional yet friendly tone appropriate for LinkedIn
8. **Natural Flow:** Use contractions, casual phrases, and human-like expressions when appropriate
9. **Spontaneity:** Make each comment feel unique and organically written, not template-driven
10. **Focus on Key Points:** If this is a long post, focus your comment on the main message or key insights

**SPECIAL INSTRUCTIONS FOR LONG POSTS:**
- Don't try to address every point in a long post
- Pick one or two key themes that resonate most
- Make your comment feel thoughtful and considered, not rushed
- Show that you've read and understood the main message
- Choose an opening that reflects the specific content, not a generic starter

**ABSOLUTELY AVOID:**
- Generic openings like "Great point!", "Absolutely right!", "This is so true!", "Such an important reminder!"
- Template-driven responses that could apply to any post
- Repetitive comment structures or predictable patterns
- AI-like phrases or corporate speak
- Formulaic beginnings that don't relate to the specific content
- Starting comments the same way repeatedly
- Generic enthusiasm without specific connection to the content
- Overly formal or robotic language
- Self-promotion or sales pitches
- Trying to summarize the entire post instead of engaging with key points

**OUTPUT:** Generate only the comment text, nothing else. No prefacing text, no quotes, no formatting - just the raw comment ready to post.
"""

    # Add specific Hinglish instructions if detected
    if detected_language == "hinglish":
        prompt += f"""

**CRITICAL HINGLISH REQUIREMENTS:**
- Use moderate Hinglish style: Primarily English structure with selective Hindi words (20-30% Hindi words maximum)
- Strategic Hindi word placement: Common expressions ('bilkul', 'sahi', 'yaar'), emphasis ('bahut', 'really'), connectors ('matlab', 'basically')
- Maintain professional tone suitable for LinkedIn with English-dominant structure
- Use creative, varied openings from the strategies above - don't default to "Bilkul sahi!" or "Great point yaar!"
- Examples of diverse moderate Hinglish comments:
  * "This really resonates with me, bilkul same experience..."
  * "Love this perspective yaar, reminds me of when..."
  * "Such an important reminder, bahut needed today..."
  * "I'm curious about this approach, matlab how do you..."
  * "From my experience, this pattern holds true..."
- AVOID: Heavy Hindi phrases, complex Hindi sentence structures, Hindi-dominant comments, or repetitive openings
- MAINTAIN: English grammar and sentence structure as the foundation with creative variety
"""
    
    return prompt


def model_call_for_comment_generation(prompt: str, model_name: str = None, post_length: int = 0, post_content: str = "") -> str:
    """
    Call the model for comment generation with optimized parameters and diversity enhancement.

    Args:
        prompt (str): The formatted prompt
        model_name (str): Optional model name
        post_length (int): Length of original post for parameter adjustment
        post_content (str): Original post content for diversity seeding

    Returns:
        str: Generated comment content
    """
    try:
        # Adjust max_tokens based on post length - longer posts may warrant slightly longer comments
        if post_length > 1500:
            max_tokens = 250  # Allow for more thoughtful responses to long posts
            base_temperature = 0.7  # Slightly lower temperature for more focused responses
        else:
            max_tokens = 180  # Standard for shorter posts
            base_temperature = 0.75  # Standard temperature

        # Add content-based temperature variation for diversity (±0.1 variation)
        content_hash = generate_content_hash(post_content, "temperature")
        temp_variation = (int(content_hash[:4], 16) % 21 - 10) / 100  # -0.1 to +0.1
        temperature = max(0.3, min(1.0, base_temperature + temp_variation))

        # Content-based top_p variation for additional diversity
        top_p_variation = (int(content_hash[4:8], 16) % 21 - 10) / 200  # -0.05 to +0.05
        top_p = max(0.1, min(1.0, 0.9 + top_p_variation))

        # Enhanced system prompt with diversity emphasis
        system_prompt = """You are an expert at writing authentic, engaging LinkedIn comments that sound naturally human-written and respond thoughtfully to the content.

CRITICAL: Each comment must be unique and contextually specific. Avoid any formulaic patterns or repetitive openings. Focus on creating a spontaneous, authentic response that directly engages with the specific content of the post."""

        # Use model manager for consistent model calling with enhanced diversity parameters
        result = model_manager.call_model(
            prompt=prompt,
            model_name=model_name or "azure/gpt-4o",  # Default to GPT-4o
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            system_prompt=system_prompt
        )

        return result

    except Exception as e:
        logger.error(f"Model call failed for comment generation: {str(e)}")
        raise Exception(f"Comment generation model call failed: {str(e)}")


def validate_comment_request(data: dict) -> None:
    """
    Validate the required fields for comment generation.

    Args:
        data (dict): Request data to validate

    Raises:
        ValueError: If required fields are missing or invalid
    """
    required_fields = ["post_content", "comment_type"]
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    # Validate post_content
    if not isinstance(data["post_content"], str):
        raise ValueError("post_content must be a string")

    if len(data["post_content"].strip()) == 0:
        raise ValueError("post_content cannot be empty")

    # Validate comment_type
    if not isinstance(data["comment_type"], str):
        raise ValueError("comment_type must be a string")

    valid_types = ["positive", "constructive", "question", "personal_experience", "insight"]
    if data["comment_type"].lower() not in valid_types:
        raise ValueError(f"Invalid comment_type. Must be one of: {', '.join(valid_types)}")


def generate_linkedin_comment(post_content: str, comment_type: str, model_name: str = None) -> str:
    """
    Generate authentic, human-like LinkedIn comments based on post content and comment type.
    
    Args:
        post_content (str): The original LinkedIn post content to comment on
        comment_type (str): Type of comment - "positive", "constructive", "question", "personal_experience", "insight"
        model_name (str): Optional model name for content generation (defaults to "azure/gpt-4o")
        
    Returns:
        str: Generated LinkedIn comment ready for posting
        
    Raises:
        Exception: If comment generation fails
    """
    try:
        logger.info(f"Starting LinkedIn comment generation with type: {comment_type}")
        
        # Validate comment type
        valid_types = ["positive", "constructive", "question", "personal_experience", "insight"]
        if comment_type.lower() not in valid_types:
            raise ValueError(f"Invalid comment type. Must be one of: {', '.join(valid_types)}")
        
        # Detect language of the post content
        detected_language = detect_language(post_content)
        logger.info(f"Detected language: {detected_language}")
        
        # Create specialized prompt for comment generation
        prompt = create_comment_prompt(post_content, comment_type, detected_language)

        # Generate comment using the model (pass post length and content for parameter optimization and diversity)
        response = model_call_for_comment_generation(prompt, model_name, len(post_content), post_content)

        # Simple cleaning for comments - avoid aggressive post-processing that might remove content
        cleaned_response = response.strip()

        # Remove any unwanted formatting but preserve content
        cleaned_response = re.sub(r'^["\'`]|["\'`]$', '', cleaned_response)  # Remove quotes at start/end
        cleaned_response = re.sub(r'\n+', ' ', cleaned_response)  # Replace newlines with spaces
        cleaned_response = re.sub(r'\s+', ' ', cleaned_response)   # Normalize whitespace
        cleaned_response = cleaned_response.strip()

        # If still empty after cleaning, return the original response with minimal cleaning
        if not cleaned_response:
            cleaned_response = response.strip()
            logger.warning(f"Cleaning resulted in empty response, using original: {cleaned_response}")

        logger.info(f"Successfully generated {comment_type} comment with {len(cleaned_response)} characters")
        return cleaned_response
        
    except Exception as e:
        logger.error(f"LinkedIn comment generation failed: {str(e)}")
        raise Exception(f"LinkedIn comment generation failed: {str(e)}")


# Production-ready module - no test functions or hardcoded content
# For testing, use the dedicated test scripts or API endpoints
