#!/usr/bin/env python3
"""
Test script for LinkedIn Comment Generation API with long posts
"""

import requests
import json

def test_long_post_comments():
    """Test the comment generation API with very long LinkedIn posts."""
    
    base_url = "http://127.0.0.1:8001/process"
    headers = {
        "Content-Type": "application/json",
        "Helicone-Auth": "Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva"
    }
    
    # Long post example from the user
    long_post = """People think they need a business plan.

They don't.

People think they need permission to pivot their career.

They don't.

People think they need approval to build something new.

They don't.

The biggest lie we tell ourselves?

"I need to wait for the right moment."
"I need someone to tell me it's okay."
"I need more experience first."

Here's the truth:

You already have everything you need to start.

The work isn't waiting for permission.
The work is doing the thing.

How many people do you know who spend months planning instead of building?

How many people ask everyone else what they should do instead of just trying?

The most successful business owners I know didn't wait.

They started messy.
They figured it out along the way.
They gave themselves permission to begin.

Your competition isn't other people.

It's your own hesitation.

Stop asking if you can.
Start doing what you want.

You can borrow my entire playbook, which I've built over the last six years."""
    
    # Another long post example
    long_post_2 = """I used to think networking was about collecting business cards.

I was wrong.

Real networking isn't about what you can get.
It's about what you can give.

Last week, I connected two people from my network.
One needed a graphic designer.
The other was a freelance designer looking for clients.

I didn't get anything out of it.
But both of them did.

And that's the point.

When you focus on helping others first, three things happen:

1. People remember you
2. They trust you
3. They want to help you back

I've built my entire career on this principle.

Not by asking "What can you do for me?"
But by asking "How can I help you?"

The best opportunities I've ever received came from people I helped first.

The job that changed my life? Came from someone I introduced to a potential client.
The speaking gig that launched my consulting business? Came from someone I mentored for free.
The partnership that doubled my revenue? Came from someone I connected with their dream hire.

Your network isn't your net worth.
Your network is your net give.

Stop thinking about what you can extract.
Start thinking about what you can contribute.

The returns will surprise you."""
    
    # Test cases for different comment types on long posts
    test_cases = [
        {
            "name": "Long Post - Positive Comment",
            "post": long_post,
            "comment_type": "positive"
        },
        {
            "name": "Long Post - Question Comment", 
            "post": long_post,
            "comment_type": "question"
        },
        {
            "name": "Long Post - Personal Experience",
            "post": long_post,
            "comment_type": "personal_experience"
        },
        {
            "name": "Long Post - Constructive Comment",
            "post": long_post,
            "comment_type": "constructive"
        },
        {
            "name": "Long Post - Insight Comment",
            "post": long_post,
            "comment_type": "insight"
        },
        {
            "name": "Long Post 2 - Personal Experience",
            "post": long_post_2,
            "comment_type": "personal_experience"
        },
        {
            "name": "Long Post 2 - Question Comment",
            "post": long_post_2,
            "comment_type": "question"
        }
    ]
    
    print("🚀 Testing LinkedIn Comment Generation API with Long Posts")
    print("=" * 70)
    print(f"📏 Long Post 1 Length: {len(long_post)} characters")
    print(f"📏 Long Post 2 Length: {len(long_post_2)} characters")
    print("=" * 70)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 50)
        
        payload = {
            "data": {
                "post_content": test_case["post"],
                "comment_type": test_case["comment_type"],
                "model_name": "azure/gpt-4o"
            },
            "operation": "comment_generation"
        }
        
        try:
            response = requests.post(base_url, headers=headers, json=payload, timeout=45)
            
            if response.status_code == 200:
                result = response.json()
                comment = result.get("result", "")
                print(f"✅ Success!")
                print(f"📝 Generated Comment: {comment}")
                print(f"📊 Comment Length: {len(comment)} characters")
                print(f"📊 Word Count: {len(comment.split())} words")
                print(f"🎯 Comment Type: {test_case['comment_type']}")
                
                # Check if comment is appropriate length for LinkedIn
                word_count = len(comment.split())
                if word_count <= 60:
                    print(f"✅ Appropriate length for LinkedIn comments")
                else:
                    print(f"⚠️  Comment might be a bit long for LinkedIn ({word_count} words)")
                    
            else:
                print(f"❌ Failed with status {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")
    
    print("\n" + "=" * 70)
    print("🏁 Long post testing completed!")

if __name__ == "__main__":
    test_long_post_comments()
