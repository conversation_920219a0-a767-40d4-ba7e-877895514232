from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union
from On_The_Go.On_the_go_content_generation import *
from Short_Content.short_content_generation import *
from Long_content.long_content_generation import *
import importlib.util
import sys
import os

# Import from Chrome Extension Comment folder
chrome_extension_dir = os.path.join(os.path.dirname(__file__), "Chrome Extension Comment")
if chrome_extension_dir not in sys.path:
    sys.path.insert(0, chrome_extension_dir)

spec_comment = importlib.util.spec_from_file_location("comment_generator", os.path.join(chrome_extension_dir, "comment_generator.py"))
comment_generator_module = importlib.util.module_from_spec(spec_comment)
spec_comment.loader.exec_module(comment_generator_module)
generate_linkedin_comment = comment_generator_module.generate_linkedin_comment
validate_comment_request = comment_generator_module.validate_comment_request

# Import from Rewrite Content folder
spec = importlib.util.spec_from_file_location("content_rewrite", os.path.join(os.path.dirname(__file__), "Rewrite Content", "content_rewrite.py"))
content_rewrite_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(content_rewrite_module)
content_rewrite_generation = content_rewrite_module.content_rewrite_generation
content_repurpose_generation = content_rewrite_module.content_repurpose_generation

# Import from Image Generation folder
image_generation_dir = os.path.join(os.path.dirname(__file__), "Image Generation")
if image_generation_dir not in sys.path:
    sys.path.insert(0, image_generation_dir)

spec_image = importlib.util.spec_from_file_location("image_generation", os.path.join(image_generation_dir, "image_generation.py"))
image_generation_module = importlib.util.module_from_spec(spec_image)
spec_image.loader.exec_module(image_generation_module)
image_generation = image_generation_module.image_generation

# Import from Carousel Content Generator folder
carousel_generation_dir = os.path.join(os.path.dirname(__file__), "Carousel Content Generator")
if carousel_generation_dir not in sys.path:
    sys.path.insert(0, carousel_generation_dir)

spec_carousel = importlib.util.spec_from_file_location("carousel_content_generation", os.path.join(carousel_generation_dir, "carousel_content_generation.py"))
carousel_generation_module = importlib.util.module_from_spec(spec_carousel)
spec_carousel.loader.exec_module(carousel_generation_module)
carousel_content_generation = carousel_generation_module.carousel_content_generation

# Import from Opinion Generator folder
opinion_generator_dir = os.path.join(os.path.dirname(__file__), "Opinion Generator")
if opinion_generator_dir not in sys.path:
    sys.path.insert(0, opinion_generator_dir)

spec_opinion = importlib.util.spec_from_file_location("opinion_generator", os.path.join(opinion_generator_dir, "opinion_generator.py"))
opinion_generator_module = importlib.util.module_from_spec(spec_opinion)
spec_opinion.loader.exec_module(opinion_generator_module)
opinion_generator = opinion_generator_module.opinion_generator
validate_opinion_request = opinion_generator_module.validate_opinion_request

# Import from Content Summarizer folder
content_summarizer_dir = os.path.join(os.path.dirname(__file__), "Content Summarizer")
if content_summarizer_dir not in sys.path:
    sys.path.insert(0, content_summarizer_dir)

spec_summarizer = importlib.util.spec_from_file_location("content_summarizer", os.path.join(content_summarizer_dir, "content_summarizer.py"))
content_summarizer_module = importlib.util.module_from_spec(spec_summarizer)
spec_summarizer.loader.exec_module(content_summarizer_module)
content_summarizer = content_summarizer_module.content_summarizer
validate_summary_request = content_summarizer_module.validate_summary_request

import random
from litellm import completion
import time
import json
from MongoDB_Utility.store_response import *
from datetime import datetime, timezone
import re
from post_processing_layer.post_proc import *
from Model_call.call_model import *
from Model_call.model_manager import get_model_manager
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient  # For async MongoDB operations
from openai import AzureOpenAI
from dotenv import load_dotenv
from pathlib import Path



# Load environment variables for Azure OpenAI
BASE_DIR = Path(__file__).resolve().parent
load_dotenv(dotenv_path=BASE_DIR / ".env")

# Initialize Azure OpenAI client
azure_client = AzureOpenAI(
    api_version=os.getenv("DALLE_AZURE_API_VERSION"),
    azure_endpoint=os.getenv("DALLE_AZURE_ENDPOINT"),
    api_key=os.getenv("DALLE_AZURE_OPENAI_API_KEY")
)

# Initialize model manager
model_manager = get_model_manager()

def call_image_generation_model(prompt, model_name=None):
    """
    Enhanced image generation with multi-model support, S3 storage, and Helicone logging.
    """
    try:
        # Use the enhanced image generation module with DALL-E 3 as default
        result = image_generation(prompt, model_name or "dall-e-3")
        return result
    except Exception as e:
        raise Exception(f"Error generating image: {str(e)}")

# Initialize FastAPI app
app = FastAPI(
    title="Enhanced Data Processing API",
    description="API for processing various data operations with multi-model Azure OpenAI support",
    version="2.0.0"
)

# Request Models
class PayloadRequest(BaseModel):
    data: Dict[str, Any] = Field(..., description="Data to be processed")
    operation: str = Field(..., description="Operation to perform on the data")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Optional parameters for the operation")

    class Config:
        schema_extra = {
            "example": {
                "data": {
                    "topic": "Cloud Computing",
                    "tone": "Business Professional",
                    "target_audience": "Business Professionals",
                    "model_name": "azure/gpt-4o",
                    "long_content": "Detailed exploration of cloud computing benefits for enterprise transformation",
                    "rewrite_content": "Original content to be refined and optimized for better engagement"
                },
                "operation": "short_content",
                "parameters": {
                    "creativity_level": "medium",
                    "cost_preference": "balanced"
                }
            }
        }

class ModelSelectionRequest(BaseModel):
    content_type: str = Field(..., description="Type of content (general, creative, technical)")
    complexity: str = Field(default="medium", description="Complexity level (simple, medium, complex)")
    cost_preference: str = Field(default="balanced", description="Cost preference (budget, balanced, premium)")

    class Config:
        schema_extra = {
            "example": {
                "content_type": "creative",
                "complexity": "medium",
                "cost_preference": "balanced"
            }
        }

class ModelComparisonRequest(BaseModel):
    prompt: str = Field(..., description="Prompt to test with multiple models")
    models: List[str] = Field(..., description="List of models to compare")
    temperature: Optional[float] = Field(default=0.5, description="Sampling temperature")
    max_tokens: Optional[int] = Field(default=250, description="Maximum tokens to generate")

    class Config:
        schema_extra = {
            "example": {
                "prompt": "Write a brief introduction about artificial intelligence",
                "models": ["azure/gpt-4o", "azure/gpt-4.1", "azure/grok-3"],
                "temperature": 0.5,
                "max_tokens": 200
            }
        }

# Response Models
class PayloadResponse(BaseModel):
    result: Any
    status: str
    message: str

class ErrorResponse(BaseModel):
    detail: str
    status_code: int

# Available operations
AVAILABLE_OPERATIONS = {
    "long_content": "Will create long content of about 200-300 words with model selection support.",
    "short_content": "Will create short content of about 80-100 words with model selection support.",
    "on_the_go_content": "Will create On-The-Go content of about 40-50 words with model selection support.",
    "repurpose_content": "Will repurpose and rewrite existing content to be authentically human-written, maintaining similar length while avoiding AI detection patterns.",
    "carousel_content": "Will generate engaging carousel content with multiple slides from topics, articles, or text content with authentic human-written style and Helicone logging.",
    "opinion_generator": "Will generate authentic, human-written social media posts expressing opinions (agree/disagree) about articles from provided URLs, grounded strictly in source material.",
    "create_summary": "Will generate concise, accurate summaries of articles from URLs with customizable length (short/medium/long) and authentic human-written style.",
    "generate_image": "Will generate a high-quality, social media-ready image using DALL-E 3 or Bria models with automatic S3 storage and Helicone logging.",
    "comment_generation": "Will generate authentic, human-like LinkedIn comments based on post content and comment type with automatic language detection (English, Hindi, Hinglish).",
    "creative_content": "Will create creative content using specialized models for creativity.",
    "technical_content": "Will create technical content using models optimized for technical accuracy.",
    "model_comparison": "Will compare output from multiple models for the same prompt.",
    "optimal_model_selection": "Will recommend the optimal model for your specific use case."
}

# Async content generation functions
async def long_content(topic, tone, target_audience, framework, model_name, long_content=None, rewrite_content=None):
    # Wrap the synchronous function in an executor to prevent blocking
    res1 = await asyncio.get_event_loop().run_in_executor(
        None,
        long_content_generation,
        topic, tone, target_audience, framework, model_name, long_content, rewrite_content
    )
    return res1

async def short_content(topic, tone, target_audience, model_name, long_content=None, rewrite_content=None):
    res2 = await asyncio.get_event_loop().run_in_executor(
        None,
        short_content_generation,
        topic, tone, target_audience, model_name, long_content, rewrite_content
    )
    return res2

async def rewrite_content(topic, tone, target_audience, model_name, long_content=None, rewrite_content=None, original_content=None):
    res3 = await asyncio.get_event_loop().run_in_executor(
        None,
        content_rewrite_generation,
        topic, tone, target_audience, model_name, long_content, rewrite_content, original_content
    )
    return res3

async def repurpose_content(content_input, tone, target_audience, model_name):
    """
    Repurpose existing content to be authentically human-written while maintaining similar length.
    Uses the same content generation patterns as other modules for consistency.
    """
    res4 = await asyncio.get_event_loop().run_in_executor(
        None,
        content_repurpose_generation,
        content_input, tone, target_audience, model_name
    )
    return res4

async def On_the_go_content(industry_topic_list, number_of_topic, model):
    res3 = await asyncio.get_event_loop().run_in_executor(
        None,
        on_the_go_promtps_selection,
        industry_topic_list, number_of_topic, model
    )
    return res3

async def generate_image(prompt: str, model_name: Optional[str] = None):
    """
    Enhanced async image generation with multi-model support and S3 storage.
    """
    response = await asyncio.get_event_loop().run_in_executor(
        None,
        call_image_generation_model,
        prompt, model_name
    )
    return response

async def comment_generation(post_content: str, comment_type: str, model_name: Optional[str] = None):
    """
    Async wrapper for LinkedIn comment generation.
    """
    response = await asyncio.get_event_loop().run_in_executor(
        None,
        generate_linkedin_comment,
        post_content, comment_type, model_name
    )
    return response

async def carousel_content(
    generation_mode: str,
    number_of_slides: int,
    tone: str = "Informative",
    target_audience: str = "General",
    model_name: str = None,
    topic: str = None,
    article_url: str = None,
    text_content: str = None
):
    """
    Async wrapper for carousel content generation.
    """
    response = await asyncio.get_event_loop().run_in_executor(
        None,
        carousel_content_generation,
        generation_mode, number_of_slides, tone, target_audience, model_name,
        topic, article_url, text_content
    )
    return response

async def opinion_content(
    url: str,
    stance: str,
    tone: str = "professional",
    platform: str = "general",
    model_name: str = None
):
    """
    Async wrapper for opinion generator.
    """
    response = await asyncio.get_event_loop().run_in_executor(
        None,
        opinion_generator,
        url, stance, tone, platform, model_name
    )
    return response

async def summary_content(
    url: str,
    summary_length: str = "medium",
    tone: str = "neutral",
    platform: str = "general",
    model_name: str = None
):
    """
    Async wrapper for content summarizer.
    """
    response = await asyncio.get_event_loop().run_in_executor(
        None,
        content_summarizer,
        url, summary_length, tone, platform, model_name
    )
    return response

# Async MongoDB operations
async def insert_into_mongodb_long_async(topic, tone, target_audience, framework, content, operation):
    # Replace with your async MongoDB implementation
    await asyncio.get_event_loop().run_in_executor(
        None,
        insert_into_mongodb_long,
        topic, tone, target_audience, framework, content, operation
    )

async def insert_into_mongodb_short_async(topic, tone, target_audience, content, operation):
    await asyncio.get_event_loop().run_in_executor(
        None,
        insert_into_mongodb_short,
        topic, tone, target_audience, content, operation
    )

async def insert_into_mongodb_otg_async(industry_topic_list, content, operation):
    await asyncio.get_event_loop().run_in_executor(
        None,
        insert_into_mongodb_otg,
        industry_topic_list, content, operation
    )

# Async function to insert image data into MongoDB
async def insert_into_mongodb_image_async(image_data, operation):
    await asyncio.get_event_loop().run_in_executor(
        None,
        insert_into_mongodb_image,
        image_data, operation
    )


def validate_content_request(data: Dict[str, Any]):
    """Validate the required fields for content generation."""
    required_fields = ["topic", "tone", "target_audience"]
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    if not all(isinstance(data[field], str) for field in required_fields):
        raise ValueError("All fields (topic, tone, target_audience) must be strings")

def validate_repurpose_request(data: Dict[str, Any]):
    """Validate the required fields for content repurposing."""
    required_fields = ["content"]
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    if not isinstance(data["content"], str):
        raise ValueError("Content field must be a string")

    if len(data["content"].strip()) == 0:
        raise ValueError("Content field cannot be empty")

def validate_carousel_request(data: Dict[str, Any]):
    """Validate the required fields for carousel content generation."""
    required_fields = ["generation_mode", "number_of_slides"]
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    # Validate generation_mode
    valid_modes = ["from_topic", "from_article", "from_text"]
    if data["generation_mode"] not in valid_modes:
        raise ValueError(f"Invalid generation_mode. Must be one of: {valid_modes}")

    # Validate number_of_slides
    if not isinstance(data["number_of_slides"], int):
        raise ValueError("number_of_slides must be an integer")

    if data["number_of_slides"] < 3 or data["number_of_slides"] > 10:
        raise ValueError("number_of_slides must be between 3 and 10")

    # Validate mode-specific requirements
    if data["generation_mode"] == "from_topic" and "topic" not in data:
        raise ValueError("topic is required for 'from_topic' mode")

    if data["generation_mode"] == "from_article" and "article_url" not in data:
        raise ValueError("article_url is required for 'from_article' mode")

    if data["generation_mode"] == "from_text" and "text_content" not in data:
        raise ValueError("text_content is required for 'from_text' mode")

async def process_payload(data: Dict[str, Any], operation: str, parameters: Optional[Dict[str, Any]] = None) -> Any:
    """Process the payload based on the operation specified."""
    try:
        if operation not in AVAILABLE_OPERATIONS:
            raise ValueError(f"Unsupported operation. Available operations: {', '.join(AVAILABLE_OPERATIONS.keys())}")

        if operation in ["long_content", "short_content"]:
            validate_content_request(data)

            topic = data["topic"]
            tone = data["tone"]
            target_audience = data["target_audience"]
            model_name = data.get("model_name", None)

            if operation == "long_content":
                framework = data["framework"]
                # Extract new optional parameters
                long_content_param = data.get("long_content", None)
                rewrite_content_param = data.get("rewrite_content", None)

                content = await long_content(topic, tone, target_audience, framework, model_name, long_content_param, rewrite_content_param)
                operation = "Long_Content"
                await insert_into_mongodb_long_async(topic, tone, target_audience, framework, content, operation)
                return content

            elif operation == "short_content":
                # Extract new optional parameters
                long_content_param = data.get("long_content", None)
                rewrite_content_param = data.get("rewrite_content", None)

                content = await short_content(topic, tone, target_audience, model_name, long_content_param, rewrite_content_param)
                operation = "Short_Content"
                await insert_into_mongodb_short_async(topic, tone, target_audience, content, operation)
                return content

            elif operation == "rewrite_content":
                # Extract optional parameters for rewrite content
                long_content_param = data.get("long_content", None)
                rewrite_content_param = data.get("rewrite_content", None)
                original_content = data.get("original_content", None)

                content = await rewrite_content(topic, tone, target_audience, model_name, long_content_param, rewrite_content_param, original_content)
                operation = "Rewrite_Content"
                await insert_into_mongodb_short_async(topic, tone, target_audience, content, operation)
                return content

        elif operation == "on_the_go_content":
            model_name = data.get("model_name", None)
            industry_topic_list = data.get("industry_topic_list", None)
            number_of_topic = data.get("number_of_topic", None)

            content = await On_the_go_content(industry_topic_list, number_of_topic, model_name)
            operation = "On_The_GO"
            await insert_into_mongodb_otg_async(industry_topic_list, content, operation)
            return content

        elif operation == "repurpose_content":
            # Validate required fields for content repurposing
            validate_repurpose_request(data)

            content_input = data["content"]
            tone = data.get("tone", "professional")
            target_audience = data.get("target_audience", "general")
            model_name = data.get("model_name", None)

            # Generate repurposed content
            content = await repurpose_content(content_input, tone, target_audience, model_name)
            operation = "Repurpose_Content"

            # Store in MongoDB (optional - following existing pattern)
            try:
                await insert_into_mongodb_short_async(content_input[:100], tone, target_audience, content, operation)
            except Exception as mongo_error:
                logger.warning(f"MongoDB insertion failed for repurpose_content: {str(mongo_error)}")

            return content

        elif operation == "carousel_content":
            # Validate required fields for carousel content generation
            validate_carousel_request(data)

            generation_mode = data["generation_mode"]
            number_of_slides = data["number_of_slides"]
            tone = data.get("tone", "Informative")
            target_audience = data.get("target_audience", "General")
            model_name = data.get("model_name", None)

            # Extract mode-specific parameters
            topic = data.get("topic", None)
            article_url = data.get("article_url", None)
            text_content = data.get("text_content", None)

            # Generate carousel content
            content = await carousel_content(
                generation_mode, number_of_slides, tone, target_audience, model_name,
                topic, article_url, text_content
            )
            operation = "Carousel_Content"

            # Store in MongoDB (optional - following existing pattern)
            try:
                # Create a summary for MongoDB storage
                content_summary = f"{generation_mode}_{number_of_slides}_slides"
                if topic:
                    content_summary += f"_{topic[:50]}"
                elif article_url:
                    content_summary += f"_{article_url[:50]}"
                elif text_content:
                    content_summary += f"_{text_content[:50]}"

                await insert_into_mongodb_short_async(content_summary, tone, target_audience, content, operation)
            except Exception as mongo_error:
                logger.warning(f"MongoDB insertion failed for carousel_content: {str(mongo_error)}")

            return content

        elif operation == "opinion_generator":
            # Validate required fields for opinion generation
            validate_opinion_request(data)

            url = data["url"]
            stance = data["stance"]
            tone = data.get("tone", "professional")
            platform = data.get("platform", "general")
            model_name = data.get("model_name", None)

            # Generate opinion content
            content = await opinion_content(url, stance, tone, platform, model_name)
            operation = "Opinion_Generator"

            # Store in MongoDB (optional - following existing pattern)
            try:
                # Create a summary for MongoDB storage
                content_summary = f"{stance}_{url[:100]}"
                await insert_into_mongodb_short_async(content_summary, tone, platform, content, operation)
            except Exception as mongo_error:
                logger.warning(f"MongoDB insertion failed for opinion_generator: {str(mongo_error)}")

            return content

        elif operation == "create_summary":
            # Validate required fields for content summarization
            validate_summary_request(data)

            url = data["url"]
            summary_length = data.get("summary_length", "medium")
            tone = data.get("tone", "neutral")
            platform = data.get("platform", "general")
            model_name = data.get("model_name", None)

            # Generate summary content
            content = await summary_content(url, summary_length, tone, platform, model_name)
            operation = "Create_Summary"

            # Store in MongoDB (optional - following existing pattern)
            try:
                # Create a summary for MongoDB storage
                content_summary = f"{summary_length}_{url[:100]}"
                await insert_into_mongodb_short_async(content_summary, tone, platform, content, operation)
            except Exception as mongo_error:
                logger.warning(f"MongoDB insertion failed for create_summary: {str(mongo_error)}")

            return content

        elif operation == "comment_generation":
            # Validate required fields for comment generation
            validate_comment_request(data)

            post_content = data["post_content"]
            comment_type = data["comment_type"]
            model_name = data.get("model_name", None)

            # Generate LinkedIn comment
            comment = await comment_generation(post_content, comment_type, model_name)
            operation = "Comment_Generation"

            # Store in MongoDB (optional - following existing pattern)
            try:
                # Create a summary for MongoDB storage
                content_summary = f"{comment_type}_{post_content[:100]}"
                await insert_into_mongodb_short_async(content_summary, comment_type, "linkedin", comment, operation)
            except Exception as mongo_error:
                logger.warning(f"MongoDB insertion failed for comment_generation: {str(mongo_error)}")

            return comment

        elif operation == "generate_image":
            prompt = data["prompt"]
            model_name = data.get("model_name", None)
            image = await generate_image(prompt, model_name)
            operation = "Generate_Image"
            # Optionally insert image metadata into MongoDB or store the image somewhere
            return image

        elif operation == "creative_content":
            validate_content_request(data)
            topic = data["topic"]
            tone = data["tone"]
            target_audience = data["target_audience"]
            creativity_level = data.get("creativity_level", "medium")

            # Use creative content generation
            content = await asyncio.get_event_loop().run_in_executor(
                None,
                model_call_for_creative_content,
                f"Create creative content about {topic} for {target_audience} with {tone} tone",
                creativity_level
            )

            return {
                "content": content,
                "metadata": {
                    "operation": "creative_content",
                    "creativity_level": creativity_level,
                    "topic": topic,
                    "tone": tone,
                    "target_audience": target_audience
                }
            }

        elif operation == "technical_content":
            validate_content_request(data)
            topic = data["topic"]
            tone = data["tone"]
            target_audience = data["target_audience"]
            complexity = data.get("complexity", "medium")

            # Use technical content generation
            content = await asyncio.get_event_loop().run_in_executor(
                None,
                model_call_for_technical_content,
                f"Create technical content about {topic} for {target_audience} with {tone} tone",
                complexity
            )

            return {
                "content": content,
                "metadata": {
                    "operation": "technical_content",
                    "complexity": complexity,
                    "topic": topic,
                    "tone": tone,
                    "target_audience": target_audience
                }
            }

        elif operation == "optimal_model_selection":
            content_type = data.get("content_type", "general")
            complexity = data.get("complexity", "medium")
            cost_preference = data.get("cost_preference", "balanced")

            optimal_model = model_manager.select_optimal_model(
                content_type=content_type,
                complexity=complexity,
                cost_preference=cost_preference
            )

            return {
                "optimal_model": optimal_model,
                "model_info": model_manager.get_model_info(optimal_model),
                "selection_criteria": {
                    "content_type": content_type,
                    "complexity": complexity,
                    "cost_preference": cost_preference
                }
            }

        else:
            raise ValueError("Operation logic is not implemented")

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# Endpoints
@app.post("/process",
         response_model=PayloadResponse,
         status_code=status.HTTP_200_OK,
         responses={ 
             400: {"model": ErrorResponse},
             500: {"model": ErrorResponse}
         })
async def process_request(request: PayloadRequest):
    """
    Process the incoming payload based on the specified operation.
    """
    try:
        result = await process_payload(
            request.data,
            request.operation,
            request.parameters
        )
        
        return PayloadResponse(
            result=result,
            status="success",
            message=f"Operation '{request.operation}' completed successfully"
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )

@app.get("/operations",
         status_code=status.HTTP_200_OK)
async def get_available_operations():
    """Get list of all available operations and their descriptions."""
    return AVAILABLE_OPERATIONS

@app.get("/health",
         status_code=status.HTTP_200_OK)
async def health_check():
    """Enhanced health check endpoint with model information."""
    try:
        endpoint_health = model_manager.get_endpoint_health()
        available_models = len(model_manager.get_available_models())

        return {
            "status": "healthy",
            "timestamp": time.time(),
            "version": app.version,
            "service": "Enhanced Data Processing API",
            "endpoints_healthy": all(endpoint_health.values()),
            "available_models": available_models,
            "endpoint_status": endpoint_health,
            "features": [
                "Multi-model Azure OpenAI support",
                "Intelligent model routing",
                "Cost optimization",
                "Fallback mechanisms",
                "Creative and technical content generation",
                "Model comparison capabilities"
            ]
        }
    except Exception as e:
        return {
            "status": "degraded",
            "timestamp": time.time(),
            "version": app.version,
            "error": str(e)
        }

@app.post("/generate_image",
          response_model=PayloadResponse,
          status_code=status.HTTP_200_OK,
          responses={
              400: {"model": ErrorResponse},
              500: {"model": ErrorResponse}
          })
async def generate_image_request(request: PayloadRequest):
    """
    Enhanced image generation endpoint with multi-model support and S3 storage.

    Supports the following request format:
    {
        "data": {
            "prompt": "A futuristic AI robot helping doctors in a modern hospital setting, digital art style",
            "model_name": "dall-e-3"  // or "bria-2-3-fast-gen2"
        },
        "operation": "generate_image"
    }

    Returns:
    {
        "result": {
            "success": true,
            "image_url": "https://growero-staging.s3.amazonaws.com/ai-generations/generated_image_123.png",
            "model_used": "dall-e-3",
            "enhanced_prompt": "enhanced prompt that was actually used",
            "metadata": {
                "original_prompt": "user's original prompt",
                "generation_time": "timestamp",
                "filename": "generated_image_123.png"
            }
        },
        "status": "success",
        "message": "Image generation completed successfully"
    }
    """
    try:
        # Validate that prompt is provided for image generation
        if "prompt" not in request.data:
            raise ValueError("Missing required field: 'prompt'")

        prompt = request.data["prompt"]
        model_name = request.data.get("model_name", "dall-e-3")  # Default to DALL-E 3

        # Validate model name
        supported_models = ["dall-e-3", "azure/dall-e-3", "bria-2-3-fast-gen2", "bria"]
        if model_name not in supported_models:
            raise ValueError(f"Unsupported model: {model_name}. Supported models: {', '.join(supported_models)}")

        # Generate the image
        image_result = await generate_image(prompt, model_name)

        # Store image metadata in MongoDB (optional)
        try:
            if image_result.get("success"):
                insert_image_data_into_mongodb(
                    prompt_instruction=image_result.get("enhanced_prompt", prompt),
                    image_url=image_result.get("image_url", ""),
                    operation="generate_image"
                )
        except Exception as mongo_error:
            # Log MongoDB error but don't fail the request
            logger.warning(f"MongoDB insertion failed: {str(mongo_error)}")

        return PayloadResponse(
            result=image_result,
            status="success" if image_result.get("success") else "error",
            message="Image generation completed successfully" if image_result.get("success") else f"Image generation failed: {image_result.get('error', 'Unknown error')}"
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )


# New Model Management Endpoints

@app.get("/models/available",
         status_code=status.HTTP_200_OK)
async def get_available_models():
    """Get list of all available models with detailed information."""
    try:
        models = model_manager.get_available_models()
        models_info = {}

        for model in models:
            models_info[model] = model_manager.get_model_info(model)

        return {
            "available_models": models,
            "models_info": models_info,
            "total_count": len(models),
            "endpoints": list(model_manager.config.endpoint_configs.keys())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving models: {str(e)}")


@app.post("/models/select-optimal",
          status_code=status.HTTP_200_OK)
async def select_optimal_model(request: ModelSelectionRequest):
    """Select the optimal model based on content requirements."""
    try:
        optimal_model = model_manager.select_optimal_model(
            content_type=request.content_type,
            complexity=request.complexity,
            cost_preference=request.cost_preference
        )

        model_info = model_manager.get_model_info(optimal_model)

        return {
            "optimal_model": optimal_model,
            "model_info": model_info,
            "selection_criteria": {
                "content_type": request.content_type,
                "complexity": request.complexity,
                "cost_preference": request.cost_preference
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error selecting model: {str(e)}")


@app.get("/models/by-cost",
         status_code=status.HTTP_200_OK)
async def get_models_by_cost():
    """Get models grouped by cost tier."""
    try:
        cost_groups = model_manager.list_models_by_cost()
        return {
            "models_by_cost": cost_groups,
            "cost_tiers": list(cost_groups.keys()),
            "recommendations": {
                "budget": "Best for simple tasks and high-volume processing",
                "standard": "Balanced performance and cost for most use cases",
                "premium": "Highest quality for complex and critical content"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving cost information: {str(e)}")


@app.get("/models/recommended/{use_case}",
         status_code=status.HTTP_200_OK)
async def get_recommended_model(use_case: str):
    """Get recommended model for a specific use case."""
    try:
        recommended_model = model_manager.get_recommended_model(use_case)
        model_info = model_manager.get_model_info(recommended_model)

        return {
            "use_case": use_case,
            "recommended_model": recommended_model,
            "model_info": model_info,
            "available_use_cases": [
                "content_rewriting", "creative_writing", "technical_analysis",
                "simple_tasks", "conversation", "code_generation",
                "summarization", "translation"
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting recommendation: {str(e)}")


@app.post("/models/compare",
          status_code=status.HTTP_200_OK)
async def compare_models(request: ModelComparisonRequest):
    """Compare output from multiple models for the same prompt."""
    try:
        results = {}

        for model in request.models:
            try:
                result = model_manager.call_model(
                    prompt=request.prompt,
                    model_name=model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens
                )

                results[model] = {
                    "content": result,
                    "success": True,
                    "model_info": model_manager.get_model_info(model)
                }
            except Exception as e:
                results[model] = {
                    "content": None,
                    "success": False,
                    "error": str(e),
                    "model_info": model_manager.get_model_info(model)
                }

        return {
            "prompt": request.prompt,
            "parameters": {
                "temperature": request.temperature,
                "max_tokens": request.max_tokens
            },
            "results": results,
            "comparison_summary": {
                "total_models": len(request.models),
                "successful_calls": sum(1 for r in results.values() if r["success"]),
                "failed_calls": sum(1 for r in results.values() if not r["success"])
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error comparing models: {str(e)}")


@app.get("/health/endpoints",
         status_code=status.HTTP_200_OK)
async def check_endpoint_health():
    """Check health status of all Azure endpoints."""
    try:
        health_status = model_manager.get_endpoint_health()
        endpoint_configs = model_manager.config.endpoint_configs

        detailed_health = {}
        for endpoint_type, is_healthy in health_status.items():
            config = endpoint_configs.get(endpoint_type, {})
            detailed_health[endpoint_type] = {
                "healthy": is_healthy,
                "name": config.get("name", "Unknown"),
                "models": config.get("models", []),
                "priority": config.get("priority", 999),
                "cost_tier": config.get("cost_tier", "unknown")
            }

        return {
            "endpoint_health": detailed_health,
            "overall_status": "healthy" if all(health_status.values()) else "degraded",
            "healthy_endpoints": sum(1 for h in health_status.values() if h),
            "total_endpoints": len(health_status)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking endpoint health: {str(e)}")


# Monitoring and Analytics Endpoints

@app.get("/monitoring/summary",
         status_code=status.HTTP_200_OK)
async def get_monitoring_summary():
    """Get comprehensive monitoring summary."""
    try:
        return model_manager.get_monitoring_summary()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting monitoring summary: {str(e)}")


@app.get("/monitoring/model/{model_name}",
         status_code=status.HTTP_200_OK)
async def get_model_performance(model_name: str):
    """Get detailed performance report for a specific model."""
    try:
        return model_manager.get_model_performance_report(model_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting model performance: {str(e)}")


@app.get("/monitoring/system-status",
         status_code=status.HTTP_200_OK)
async def get_system_status():
    """Get overall system status and health."""
    try:
        return model_manager.get_system_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system status: {str(e)}")


@app.post("/monitoring/health-check",
          status_code=status.HTTP_200_OK)
async def run_health_checks():
    """Manually trigger health checks for all endpoints."""
    try:
        await model_manager.run_health_checks()
        return {
            "message": "Health checks completed",
            "timestamp": time.time(),
            "results": model_manager.get_endpoint_health()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running health checks: {str(e)}")


@app.get("/monitoring/errors/{model_name}",
         status_code=status.HTTP_200_OK)
async def get_model_errors(model_name: str, limit: int = 20):
    """Get recent errors for a specific model."""
    try:
        from Model_call.monitoring import model_metrics
        if not model_metrics:
            raise HTTPException(status_code=503, detail="Monitoring not available")

        errors = model_metrics.get_recent_errors(model_name, limit)
        return {
            "model_name": model_name,
            "recent_errors": errors,
            "total_errors": model_metrics.error_counts.get(model_name, 0),
            "error_rate": 1 - model_metrics.get_success_rate(model_name)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting model errors: {str(e)}")


# Performance Analytics Endpoints

@app.get("/analytics/performance-comparison",
         status_code=status.HTTP_200_OK)
async def get_performance_comparison():
    """Compare performance metrics across all models."""
    try:
        from Model_call.monitoring import model_metrics
        if not model_metrics:
            raise HTTPException(status_code=503, detail="Monitoring not available")

        models = list(model_metrics.call_counts.keys())
        comparison = {}

        for model in models:
            comparison[model] = {
                "total_calls": model_metrics.call_counts[model],
                "success_rate": model_metrics.get_success_rate(model),
                "avg_response_time": model_metrics.get_average_response_time(model),
                "health_score": model_metrics.get_model_health_score(model),
                "model_info": model_manager.get_model_info(model)
            }

        # Sort by health score
        sorted_models = sorted(comparison.items(), key=lambda x: x[1]["health_score"], reverse=True)

        return {
            "performance_comparison": dict(sorted_models),
            "best_performing_model": sorted_models[0][0] if sorted_models else None,
            "total_models": len(models),
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting performance comparison: {str(e)}")


@app.get("/analytics/usage-statistics",
         status_code=status.HTTP_200_OK)
async def get_usage_statistics():
    """Get usage statistics and trends."""
    try:
        from Model_call.monitoring import model_metrics
        if not model_metrics:
            raise HTTPException(status_code=503, detail="Monitoring not available")

        total_calls = sum(model_metrics.call_counts.values())
        total_successes = sum(model_metrics.success_counts.values())

        # Calculate usage distribution
        usage_distribution = {}
        for model, calls in model_metrics.call_counts.items():
            usage_distribution[model] = {
                "calls": calls,
                "percentage": (calls / total_calls * 100) if total_calls > 0 else 0
            }

        # Get cost tier usage
        cost_tier_usage = {"budget": 0, "standard": 0, "premium": 0}
        for model, calls in model_metrics.call_counts.items():
            model_info = model_manager.get_model_info(model)
            cost_tier = model_info.get("cost_tier", "standard")
            cost_tier_usage[cost_tier] += calls

        return {
            "total_calls": total_calls,
            "total_successes": total_successes,
            "overall_success_rate": (total_successes / total_calls) if total_calls > 0 else 0,
            "usage_distribution": usage_distribution,
            "cost_tier_usage": cost_tier_usage,
            "most_used_model": max(model_metrics.call_counts.items(), key=lambda x: x[1])[0] if model_metrics.call_counts else None,
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting usage statistics: {str(e)}")
