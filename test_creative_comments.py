#!/usr/bin/env python3
"""
Test script for Creative LinkedIn Comment Generation
"""

import requests
import json
import time

def test_creative_variety():
    """Test the creative variety in comment generation."""
    
    base_url = "http://127.0.0.1:8001/process"
    headers = {"Content-Type": "application/json"}
    
    # Test post content
    post_content = "Aaj kal sab sochte hain ki success ke liye perfect plan chahiye. Lekin sach yeh hai ki zindagi mein sab kuch plan ke hisaab se nahi hota. Jab tak aap action nahi loge, tab tak kuch bhi nahi badlega."
    
    print("🎨 Testing Creative Comment Variety")
    print("=" * 60)
    print(f"📝 Post Content: {post_content[:100]}...")
    print("=" * 60)
    
    # Test each comment type multiple times to see variety
    comment_types = ["positive", "constructive", "question", "personal_experience", "insight"]
    
    for comment_type in comment_types:
        print(f"\n🎯 Testing {comment_type.upper()} Comments:")
        print("-" * 40)
        
        for i in range(3):  # Generate 3 comments of each type
            payload = {
                "data": {
                    "post_content": post_content,
                    "comment_type": comment_type
                },
                "operation": "comment_generation"
            }
            
            try:
                response = requests.post(base_url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    comment = result.get("result", "")
                    
                    # Extract the opening phrase (first 10-15 words)
                    opening = " ".join(comment.split()[:12]) + "..."
                    
                    print(f"  {i+1}. Opening: {opening}")
                    print(f"     Full: {comment}")
                    print()
                else:
                    print(f"  {i+1}. ❌ Failed with status {response.status_code}")
                    
                # Small delay between requests
                time.sleep(2)
                
            except requests.exceptions.RequestException as e:
                print(f"  {i+1}. ❌ Request failed: {str(e)}")
            except Exception as e:
                print(f"  {i+1}. ❌ Unexpected error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🏁 Creative variety test completed!")

if __name__ == "__main__":
    test_creative_variety()
