#!/usr/bin/env python3
"""
Test script for LinkedIn Comment Generation API
"""

import requests
import json

def test_comment_generation():
    """Test the comment generation API with different comment types."""
    
    base_url = "http://127.0.0.1:8001/process"
    headers = {
        "Content-Type": "application/json",
        "Helicone-Auth": "Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva"
    }
    
    # Test cases for different comment types
    test_cases = [
        {
            "name": "Positive Comment",
            "data": {
                "post_content": "Just launched our new AI-powered analytics platform! Excited to see how it helps businesses make data-driven decisions.",
                "comment_type": "positive",
                "model_name": "azure/gpt-4o"
            }
        },
        {
            "name": "Question Comment",
            "data": {
                "post_content": "Remote work has completely changed how we collaborate. Teams are more productive than ever.",
                "comment_type": "question"
            }
        },
        {
            "name": "Personal Experience Comment",
            "data": {
                "post_content": "Leadership is about empowering others to achieve their potential, not just managing tasks.",
                "comment_type": "personal_experience"
            }
        },
        {
            "name": "Constructive Comment",
            "data": {
                "post_content": "The future of sustainable technology lies in renewable energy integration with smart grid systems.",
                "comment_type": "constructive"
            }
        },
        {
            "name": "Insight Comment",
            "data": {
                "post_content": "Machine learning is transforming healthcare diagnostics with unprecedented accuracy rates.",
                "comment_type": "insight"
            }
        },
        {
            "name": "Hinglish Post Test",
            "data": {
                "post_content": "Startup journey mein sabse important cheez hai persistence. Failures se seekhna aur aage badhna.",
                "comment_type": "personal_experience"
            }
        }
    ]
    
    print("🚀 Testing LinkedIn Comment Generation API")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        payload = {
            "data": test_case["data"],
            "operation": "comment_generation"
        }
        
        try:
            response = requests.post(base_url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                comment = result.get("result", "")
                print(f"✅ Success!")
                print(f"📝 Generated Comment: {comment}")
                print(f"📊 Length: {len(comment)} characters")
                print(f"🎯 Comment Type: {test_case['data']['comment_type']}")
            else:
                print(f"❌ Failed with status {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")

def test_error_cases():
    """Test error handling with invalid inputs."""
    
    base_url = "http://127.0.0.1:8001/process"
    headers = {"Content-Type": "application/json"}
    
    error_test_cases = [
        {
            "name": "Missing post_content",
            "data": {
                "comment_type": "positive"
            }
        },
        {
            "name": "Missing comment_type",
            "data": {
                "post_content": "Test post content"
            }
        },
        {
            "name": "Invalid comment_type",
            "data": {
                "post_content": "Test post content",
                "comment_type": "invalid_type"
            }
        },
        {
            "name": "Empty post_content",
            "data": {
                "post_content": "",
                "comment_type": "positive"
            }
        }
    ]
    
    print("\n🧪 Testing Error Handling")
    print("=" * 60)
    
    for i, test_case in enumerate(error_test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        payload = {
            "data": test_case["data"],
            "operation": "comment_generation"
        }
        
        try:
            response = requests.post(base_url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 400:
                print(f"✅ Correctly returned error 400")
                error_detail = response.json().get("detail", "")
                print(f"📝 Error message: {error_detail}")
            else:
                print(f"⚠️  Unexpected status {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    # Test successful cases
    test_comment_generation()
    
    # Test error cases
    test_error_cases()
